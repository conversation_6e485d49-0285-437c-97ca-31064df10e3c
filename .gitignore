# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# VS Code
.vscode/

# PyCharm
.idea/

# Docker
.docker/

# Temporary files
*.swp
*.swo
*~

# OS specific
.DS_Store
Thumbs.db

# Project specific
uploaded_files/
*.wav
*.mp3
*.mp4
*.avi
*.mov
*.pdf
*.docx
*.pptx
*.txt
!requirements.txt

# Temporary directories
temp/
tmp/
Modules/Transcriber/temp/

# Audio files
*.wav
*.mp3
*.flac
*.ogg
*.m4a
*.aac

# Test files
test_*.py
*_test.py
test_*.html
*_test.html
test_*.js
*_test.js
test_*.ps1
*_test.ps1

# Specific test files
create_large_test.py
create_test_audio.py
download_sample.py
test_api.py
test_local_api.py
test_with_large_sample.py
test_with_sample.py
test_api.ps1
test_transcription.ps1
test_upload.html
large_sample.mp3
sample_speech.mp3

.vs/.vs/
