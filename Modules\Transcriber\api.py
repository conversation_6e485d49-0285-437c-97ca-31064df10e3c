"""
Transcriber API Module

This module provides FastAPI routes for audio transcription.
"""

import os
import re
import logging
from fastapi import APIRouter, HTTPException, File, UploadFile, Form, Request
from pydantic import BaseModel, Field
from typing import List, Optional

from .services import S3Service, SUPPORTED_FORMATS, generate_real_estate_summary, translate_to_english, denoise_audio, upload_denoised_to_s3
from .openai_whisper import transcribe_with_openai_whisper, cleanup_temp_directory, download_from_url, save_uploaded_file

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["Transcriber"])

# Models
class S3URLRequest(BaseModel):
    s3_url: str = Field(..., description="S3 URL of the audio file to transcribe")

class GoogleSTTRequest(BaseModel):
    audio_url: Optional[str] = Field(None, description="URL of the audio file to transcribe (S3 or HTTPS)")
    use_google_stt: bool = Field(True, description="Whether to use Google Speech-to-Text for transcription")

class SpeakerSegment(BaseModel):
    speaker: str
    start: float
    end: float
    duration: float

class DiarizationMetadata(BaseModel):
    format_version: str
    audio_duration: float
    num_speakers: int
    speaker_segments: List[SpeakerSegment]

class TranscriptionResponse(BaseModel):
    text: str
    translated_text: Optional[str] = None
    language: Optional[str] = None
    duration: Optional[float] = None
    diarization: Optional[DiarizationMetadata] = None
    cleaned_audio_url: Optional[str] = None

class GoogleTranscriptionResponse(BaseModel):
    original_text: str
    language_detected: Optional[str] = None
    translated_text: Optional[str] = None
    summary: Optional[str] = None
    error: Optional[str] = None
    crisp_audio_url: Optional[str] = None  # S3 URL of the denoised audio file

# Transcription endpoint using OpenAI Whisper
@router.post("/transcribe/", response_model=GoogleTranscriptionResponse)
async def transcribe_audio(
    file: Optional[UploadFile] = File(None),
    audio_url: Optional[str] = Form(None),
    request: Request = None
):
    """
    Transcribe an audio file using OpenAI Whisper API (Translations endpoint).

    This endpoint accepts either a URL to an audio file or an uploaded audio file,
    transcribes it using OpenAI Whisper Translations API, and returns the transcription
    in English regardless of the original audio language.

    Args:
        file: Uploaded audio file (optional)
        audio_url: URL of the audio file to transcribe (optional)

    Returns:
        JSONResponse containing the transcription result in English only,
        detected original language, and a summary with key takeaways
    """
    local_path = None
    mock_text = ""
    mock_language = ""
    try:
        # Check for mock transcription from form fields (for testing)
        if mock_text:
            logger.info("Using mock transcription from form fields")
            mock_lang = mock_language or "hi"

            # Translate the mock text
            translated_text = translate_to_english(mock_text, mock_lang)

            # Generate a summary
            summary = generate_real_estate_summary(translated_text)

            return GoogleTranscriptionResponse(
                original_text=mock_text,
                translated_text=translated_text,
                language_detected=mock_lang,
                summary=summary,
                error=None,
                crisp_audio_url=None
            )

        # Check for mock transcription headers (for testing)
        if request and request.headers.get("X-Mock-Transcription"):
            logger.info("Using mock transcription from headers")
            header_mock_text = request.headers.get("X-Mock-Transcription")
            header_mock_lang = request.headers.get("X-Mock-Language", "hi")

            # Translate the mock text
            translated_text = translate_to_english(header_mock_text, header_mock_lang)

            # Generate a summary
            summary = generate_real_estate_summary(translated_text)

            return GoogleTranscriptionResponse(
                original_text=header_mock_text,
                translated_text=translated_text,
                language_detected=header_mock_lang,
                summary=summary,
                error=None,
                crisp_audio_url=None
            )

        # Handle empty string values and validate inputs
        if audio_url == "":
            audio_url = None

        # Check if file is valid (not None and not empty)
        valid_file = file is not None and hasattr(file, 'filename') and file.filename

        # Check if we have either a URL or a valid file
        if not audio_url and not valid_file:
            return GoogleTranscriptionResponse(
                original_text="",
                translated_text="",
                language_detected="en",
                summary="",
                error="Either audio_url or file must be provided",
                crisp_audio_url=None
            )

        # If we have a URL, download the file
        if audio_url:
            logger.info(f"Transcribing audio from URL: {audio_url}")
            local_path = download_from_url(audio_url)
            if not local_path:
                return GoogleTranscriptionResponse(
                    original_text="",
                    translated_text="",
                    language_detected="en",
                    summary="",
                    error=f"Failed to download file from URL: {audio_url}",
                    crisp_audio_url=None
                )

        # If we have a file, save it
        elif file:
            logger.info(f"Transcribing uploaded audio file: {file.filename}")
            try:
                # Check if file is empty
                file_content = await file.read()
                file_size = len(file_content)
                logger.info(f"Uploaded file size: {file_size} bytes")

                if file_size == 0:
                    logger.error("Uploaded file is empty (0 bytes)")
                    return GoogleTranscriptionResponse(
                        original_text="",
                        translated_text="",
                        language_detected="en",
                        summary="",
                        error="Uploaded file is empty (0 bytes)",
                        crisp_audio_url=None
                    )

                # Save the file with original filename to preserve extension
                local_path = save_uploaded_file(file_content, file.filename)
                if not local_path:
                    logger.error("Failed to save uploaded file")
                    return GoogleTranscriptionResponse(
                        original_text="",
                        translated_text="",
                        language_detected="en",
                        summary="",
                        error="Failed to save uploaded file",
                        crisp_audio_url=None
                    )

                # Log file details
                logger.info(f"Saved uploaded file to {local_path}")
                logger.info(f"File exists: {os.path.exists(local_path)}")
                logger.info(f"File size: {os.path.getsize(local_path)} bytes")

            except Exception as e:
                logger.error(f"Error processing uploaded file: {str(e)}")
                return GoogleTranscriptionResponse(
                    original_text="",
                    translated_text="",
                    language_detected="en",
                    summary="",
                    error=f"Error processing uploaded file: {str(e)}",
                    crisp_audio_url=None
                )

        # Check if the file exists and has content (single check)
        if not os.path.exists(local_path):
            logger.error(f"File does not exist: {local_path}")
            return GoogleTranscriptionResponse(
                original_text="",
                translated_text="",
                language_detected="en",
                summary="",
                error=f"File does not exist: {local_path}",
                crisp_audio_url=None
            )

        file_size = os.path.getsize(local_path)
        logger.info(f"File size before transcription: {file_size} bytes")

        if file_size == 0:
            logger.error(f"File is empty (0 bytes): {local_path}")
            return GoogleTranscriptionResponse(
                original_text="",
                translated_text="",
                language_detected="en",
                summary="",
                error="Audio file is empty (0 bytes)",
                crisp_audio_url=None
            )

        # Apply noise reduction before transcription
        logger.info(f"Applying noise reduction to audio file: {local_path}")
        crisp_s3_url = None  # Will store the S3 URL of the denoised file
        try:
            denoised_path = denoise_audio(local_path)
            if denoised_path != local_path:
                logger.info(f"Noise reduction completed. Using denoised file: {denoised_path}")

                # If input was a URL (S3), upload the denoised file back to S3
                if audio_url:
                    logger.info("Input was URL, uploading denoised file to S3...")
                    crisp_s3_url = upload_denoised_to_s3(audio_url, denoised_path)
                    if crisp_s3_url:
                        logger.info(f"Successfully uploaded denoised file to S3: {crisp_s3_url}")
                    else:
                        logger.warning("Failed to upload denoised file to S3")

                # Update local_path to use the denoised version
                original_path = local_path
                local_path = denoised_path
            else:
                logger.info("Using original file (noise reduction skipped or failed)")
        except Exception as e:
            logger.warning(f"Noise reduction failed, using original file: {str(e)}")

        # Transcribe with Whisper
        logger.info(f"Starting Whisper transcription for file: {local_path}")

        # Get file format information
        try:
            import subprocess
            import json

            # Use ffprobe to get file information
            ffprobe_cmd = [
                "ffprobe",
                "-v", "error",
                "-show_format",
                "-show_streams",
                "-print_format", "json",
                local_path
            ]

            ffprobe_result = subprocess.run(ffprobe_cmd, capture_output=True, text=True)
            if ffprobe_result.returncode == 0:
                try:
                    format_info = json.loads(ffprobe_result.stdout)
                    logger.info(f"File format: {format_info.get('format', {}).get('format_name', 'unknown')}")
                    logger.info(f"Duration: {format_info.get('format', {}).get('duration', 'unknown')} seconds")

                    # Log audio stream info if available
                    audio_streams = [s for s in format_info.get('streams', []) if s.get('codec_type') == 'audio']
                    if audio_streams:
                        stream = audio_streams[0]
                        logger.info(f"Audio codec: {stream.get('codec_name', 'unknown')}")
                        logger.info(f"Sample rate: {stream.get('sample_rate', 'unknown')} Hz")
                        logger.info(f"Channels: {stream.get('channels', 'unknown')}")
                    else:
                        logger.warning("No audio streams found in the file")
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse ffprobe output: {ffprobe_result.stdout}")
            else:
                logger.error(f"Failed to get file format info: {ffprobe_result.stderr}")
        except Exception as e:
            logger.error(f"Error checking file format: {str(e)}")

        # Call the OpenAI Whisper transcription function
        result = transcribe_with_openai_whisper(local_path)
        logger.info(f"OpenAI Whisper transcription result: {result}")

        # Check if transcription was successful
        if not result:
            logger.error("Transcription result is None")
            return GoogleTranscriptionResponse(
                original_text="",
                translated_text="",
                language_detected="en",
                summary="",
                error="Transcription failed: No result returned from OpenAI Whisper",
                crisp_audio_url=None
            )

        # Check if there's a specific error message
        if result.get("text", "").startswith("Error:"):
            error_msg = result.get("text")
            logger.error(f"Transcription error: {error_msg}")
            return GoogleTranscriptionResponse(
                original_text="",
                translated_text="",
                language_detected=result.get("language", "en"),
                summary="",
                error=error_msg,
                crisp_audio_url=None
            )

        # Check if the transcription text is empty
        if not result.get("text"):
            logger.error("Transcription text is empty")

            # Check if there's a specific error in the result
            if "error" in result:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"Transcription error: {error_msg}")
                return GoogleTranscriptionResponse(
                    original_text="",
                    translated_text="",
                    language_detected=result.get("language", "en"),
                    summary="",
                    error=f"Transcription failed: {error_msg}",
                    crisp_audio_url=None
                )

            # Check if the audio file was processed successfully
            if "segments" in result:
                logger.error("No speech detected in the audio file")
                return GoogleTranscriptionResponse(
                    original_text="",
                    translated_text="",
                    language_detected=result.get("language", "en"),
                    summary="",
                    error="No speech detected in the audio file",
                    crisp_audio_url=None
                )
            else:
                logger.error("OpenAI Whisper processing failed")
                return GoogleTranscriptionResponse(
                    original_text="",
                    translated_text="",
                    language_detected=result.get("language", "en"),
                    summary="",
                    error="OpenAI Whisper speech-to-text processing failed",
                    crisp_audio_url=None
                )

        # Generate a real estate conversation summary
        summary = ""
        try:
            # Get the translated text or use the original text if no translation
            translated_text = result.get("translated_text", result["text"])

            # Skip summary generation for empty text
            if not translated_text or len(translated_text.strip()) < 10:
                logger.warning("Text too short for summary generation")
                summary = "- Insufficient content to generate a summary."
            else:
                # Log the text being summarized
                logger.info(f"Generating summary for text: {translated_text[:100]}...")

                # Clean the text before summarization
                cleaned_text = re.sub(r'\s+', ' ', translated_text).strip()

                # Generate a real estate conversation summary
                summary = generate_real_estate_summary(cleaned_text)

                # Validate the summary
                if not summary or len(summary.strip()) < 5:
                    logger.warning("Generated summary is empty or too short")
                    summary = "- No meaningful content detected in the conversation."

                logger.info(f"Generated summary: {summary}")

        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            # Return a more graceful fallback
            summary = "- Failed to generate summary for this conversation."

        # Return the transcription with language, translation, and summary
        return GoogleTranscriptionResponse(
            original_text=result["text"],
            translated_text=result.get("translated_text", result["text"]),
            language_detected=result.get("language", "en"),
            summary=summary,
            error=None,
            crisp_audio_url=crisp_s3_url
        )

    except Exception as e:
        error_msg = f"Error processing audio: {str(e)}"
        logger.error(error_msg)

        # Create a detailed error response
        return GoogleTranscriptionResponse(
            original_text="",
            translated_text="",
            language_detected="en",
            summary="- Failed to process audio due to an error.",
            error=error_msg,
            crisp_audio_url=None
        )
    finally:
        # Clean up the downloaded/uploaded file and denoised file
        files_to_cleanup = []
        if local_path and os.path.exists(local_path):
            files_to_cleanup.append(local_path)

        # Also clean up original file if we used a denoised version
        if 'original_path' in locals() and original_path and os.path.exists(original_path) and original_path != local_path:
            files_to_cleanup.append(original_path)

        for file_path in files_to_cleanup:
            try:
                os.unlink(file_path)
                logger.debug(f"Deleted temporary file: {file_path}")
            except Exception as cleanup_error:
                logger.warning(f"Failed to delete temporary file {file_path}: {str(cleanup_error)}")

        # Clean up all temporary files in the temp directory
        cleanup_temp_directory()

# Info endpoint
@router.get("/")
async def get_info():
    """
    Get information about the Transcriber API.

    Returns:
        JSON response with API information
    """
    return {
        "name": "Audio Transcriber API",
        "version": "1.0.0",
        "description": "API for transcribing audio using OpenAI Whisper (English output only)",
        "features": [
            "Transcribes audio in any language",
            "Always returns results in English",
            "Uses OpenAI Whisper Translations API",
            "Supports multiple audio formats"
        ],
        "endpoints": {
            "transcribe": "/transcribe/",
            "docs": "/docs"
        }
    }
