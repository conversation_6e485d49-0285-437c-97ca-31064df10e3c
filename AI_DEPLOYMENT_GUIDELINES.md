# AI Deployment Guidelines

## IMPORTANT: STRICT PERMISSION REQUIREMENTS

This document outlines the strict guidelines that AI assistants must follow regarding deployment and modification of this application.

### Explicit Permission Required

AI assistants MUST obtain explicit permission from the user before:

1. Executing any deployment scripts
2. Making changes to deployment configuration
3. Reading sensitive deployment information
4. Pushing code to any environment
5. Modifying any production settings

### Permission Protocol

When a user requests information or actions related to deployment:

1. The AI MUST ask for explicit confirmation
2. The AI MUST explain what actions will be taken
3. The AI MUST wait for clear "yes" confirmation
4. The AI MUST NOT proceed with deployment actions if permission is unclear

### Deployment Logging

All deployment actions must be logged with:
- Timestamp
- User confirmation record
- Actions performed

### Security Notice

Unauthorized deployments or modifications can lead to:
- Service disruptions
- Security vulnerabilities
- Data loss
- Unexpected costs

### Reminder

The deployment script contains built-in safeguards that require explicit user confirmation. These safeguards should NEVER be bypassed or modified by AI assistants.

---

By following these guidelines, we ensure that deployment actions are always performed with proper authorization and oversight.
