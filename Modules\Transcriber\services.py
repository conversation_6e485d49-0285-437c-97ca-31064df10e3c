"""
Transcriber Services Module

This module provides utility functions for the Transcriber module.
"""

import os
import logging
import subprocess
import tempfile
import requests
import boto3
import re
from typing import Tuple, Dict, Optional
from config.settings import settings

# Import language detection
try:
    from langdetect import detect
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False
    logging.warning("langdetect not available. Language detection will be limited.")

# Translation is now handled by OpenAI Whisper translations endpoint
GOOGLE_TRANSLATE_AVAILABLE = False
GOOGLE_TRANSLATE_CLIENT = None

# Configure logging
logger = logging.getLogger(__name__)

# Configure FFmpeg paths
FFMPEG_PATH = os.getenv('FFMPEG_PATH', 'ffmpeg')
FFPROBE_PATH = os.getenv('FFPROBE_PATH', 'ffprobe')

# Supported audio formats
SUPPORTED_FORMATS = {'.wav', '.mp3', '.m4a', '.flac', '.ogg', '.aac'}

# Using Whisper API for transcription and Google Cloud for translation

def verify_ffmpeg():
    """
    Verify that <PERSON>mpeg and <PERSON>probe are installed and available.

    This function checks if FFmpeg and FFprobe are installed and available
    by running the version command. If either is not available, it raises
    a SystemExit exception.
    """
    try:
        subprocess.run([FFMPEG_PATH, '-version'], capture_output=True, check=True)
        subprocess.run([FFPROBE_PATH, '-version'], capture_output=True, check=True)
        logger.info("FFmpeg verification successful")
    except Exception as e:
        logger.error(f"FFmpeg verification failed: {str(e)}")
        raise SystemExit("FFmpeg or FFprobe not installed")


class S3Service:
    def __init__(self):
        # Get AWS credentials from environment variables or settings
        aws_access_key = settings.AWS_ACCESS_KEY_ID
        aws_secret_key = settings.AWS_SECRET_ACCESS_KEY
        aws_region = settings.AWS_REGION

        # Initialize AWS S3 client
        self.s3_client = None
        if aws_access_key and aws_secret_key:
            try:
                self.s3_client = boto3.client("s3",
                    region_name=aws_region,
                    aws_access_key_id=aws_access_key,
                    aws_secret_access_key=aws_secret_key)
                # Test the connection
                self.s3_client.list_buckets()
                logger.info("Successfully connected to AWS S3")
            except Exception as e:
                logger.error(f"Failed to initialize AWS S3 client: {str(e)}")
                logger.warning("S3 operations will fall back to HTTP requests for public files")
        else:
            logger.warning("AWS credentials not provided. S3 operations will fall back to HTTP requests")

        self.bucket_name = settings.S3_BUCKET_NAME
        self.base_folder = settings.S3_BASE_FOLDER
        self.temp_folder = settings.S3_SUBFOLDER

    def upload_to_s3(self, file_path: str, object_key: str = None) -> str:
        """Upload a file to S3 and return the S3 URL.

        Args:
            file_path: Path to the file to upload
            object_key: S3 object key (if None, will be generated from the file name)

        Returns:
            The S3 URL of the uploaded file
        """
        try:
            logger.info(f"Starting S3 upload for file: {file_path}")
            logger.info(f"Object key: {object_key}")
            logger.info(f"File exists: {os.path.exists(file_path)}")

            if os.path.exists(file_path):
                logger.info(f"File size: {os.path.getsize(file_path)} bytes")

            if not self.s3_client:
                logger.warning("S3 client not initialized. Cannot upload file.")
                return None

            # Generate object key if not provided
            if not object_key:
                file_name = os.path.basename(file_path)
                object_key = f"{self.base_folder}{self.temp_folder}cleaned_{file_name}"
                logger.info(f"Generated object key: {object_key}")

            # Log S3 configuration
            logger.info(f"S3 bucket name: {self.bucket_name}")
            logger.info(f"S3 region: {settings.AWS_REGION}")

            # Upload the file
            logger.info(f"Uploading file to S3: {os.path.basename(file_path)}")
            self.s3_client.upload_file(file_path, self.bucket_name, object_key)
            logger.info(f"File uploaded successfully to S3")

            # Generate the S3 URL
            s3_url = f"https://{self.bucket_name}.s3.{settings.AWS_REGION}.amazonaws.com/{object_key}"
            logger.info(f"Generated S3 URL: {s3_url}")

            return s3_url
        except Exception as e:
            logger.error(f"Failed to upload file to S3: {str(e)}")
            return None

    def download_from_s3(self, s3_url: str) -> str:
        """Download a file from an S3 URL and save it to a temporary file.

        Args:
            s3_url: The S3 URL of the file to download

        Returns:
            The path to the downloaded file
        """
        temp_path = None
        try:
            logger.info(f"Downloading file from S3")

            # Parse the S3 URL to extract bucket name and object key
            bucket_name = None
            object_key = None

            # Format 1: https://bucket-name.s3.region.amazonaws.com/path/to/file
            if s3_url.startswith('https://') and '.s3.' in s3_url and '.amazonaws.com/' in s3_url:
                bucket_part = s3_url.split('.s3.')[0]
                bucket_name = bucket_part.replace('https://', '')
                object_key = s3_url.split('.amazonaws.com/')[1]

            # Format 2: s3://bucket-name/path/to/file
            elif s3_url.startswith('s3://'):
                parts = s3_url[5:].split('/', 1)
                if len(parts) == 2:
                    bucket_name, object_key = parts

            # If we couldn't parse the URL, try using the configured bucket
            if not bucket_name or not object_key:
                logger.warning(f"Could not parse S3 URL format, using configured bucket")
                bucket_name = self.bucket_name
                # Try to extract the object key from the URL
                if '/' in s3_url:
                    object_key = s3_url.split('/')[-1]
                else:
                    raise ValueError(f"Could not extract object key from URL")

            # Determine file extension for the temporary file
            file_ext = os.path.splitext(object_key)[1].lower()
            if not file_ext:
                file_ext = '.mp3'  # Default to mp3 if no extension is found

            # Create a temporary file to store the downloaded content
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
                temp_path = temp_file.name

            # Download the file from S3
            success = False

            # Try using boto3 S3 client first
            if self.s3_client:
                try:
                    self.s3_client.download_file(bucket_name, object_key, temp_path)
                    logger.info(f"Successfully downloaded file from S3")
                    success = True
                except Exception as e:
                    logger.error(f"S3 download failed with boto3: {str(e)}")

            # If boto3 failed or not available, try HTTP request
            if not success:
                try:
                    logger.info(f"Attempting to download using HTTP request as fallback")
                    response = requests.get(s3_url, stream=True, timeout=30)
                    if response.status_code == 200:
                        with open(temp_path, 'wb') as f:
                            for chunk in response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        logger.info(f"Successfully downloaded file using HTTP request")
                        success = True
                    else:
                        logger.error(f"HTTP download failed with status code: {response.status_code}")
                except Exception as e:
                    logger.error(f"HTTP download failed: {str(e)}")

            # Verify the downloaded file
            if success and os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                if file_size > 0:
                    return temp_path
                else:
                    logger.error(f"Downloaded file is empty")
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    raise ValueError("Downloaded file is empty")
            else:
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                raise ValueError("Failed to download file from S3")

        except Exception as e:
            logger.error(f"Failed to download file from S3: {str(e)}")
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as cleanup_error:
                    logger.warning(f"Failed to clean up temporary file: {str(cleanup_error)}")

            # Create an empty file as a placeholder
            with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as empty_file:
                empty_path = empty_file.name
                logger.warning(f"Created empty placeholder file")
                return empty_path


def get_audio_duration(file_path: str) -> float:
    """Get the duration of an audio file in seconds using ffprobe."""
    try:
        cmd = [FFPROBE_PATH, "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        duration = float(result.stdout.strip())
        return duration
    except Exception as e:
        logger.warning(f"Failed to get audio duration: {str(e)}")
        # Return a default value
        return 60.0


def convert_to_wav(content: bytes, file_ext: str) -> Tuple[bytes, float]:
    """Convert audio to WAV format and return the content and duration."""
    with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_input:
        temp_input.write(content)
        temp_input_path = temp_input.name

    temp_output_path = temp_input_path + ".wav"

    try:
        # Convert to WAV with 16kHz sample rate and mono channel
        subprocess.run([FFMPEG_PATH, "-i", temp_input_path, "-ar", "16000", "-ac", "1", "-f", "wav", temp_output_path], check=True, capture_output=True)

        # Get the duration of the audio file
        duration = get_audio_duration(temp_output_path)

        # Read the converted WAV file
        with open(temp_output_path, "rb") as temp_output:
            wav_content = temp_output.read()

        return wav_content, duration
    finally:
        # Clean up temporary files
        if os.path.exists(temp_input_path):
            os.remove(temp_input_path)
        if os.path.exists(temp_output_path):
            os.remove(temp_output_path)


def upload_denoised_to_s3(original_s3_url: str, denoised_file_path: str) -> Optional[str]:
    """
    Upload denoised audio file to S3 with '_crisp' suffix in the same location as original.

    Args:
        original_s3_url: The original S3 URL
        denoised_file_path: Path to the denoised audio file

    Returns:
        The new S3 URL with '_crisp' suffix, or None if upload fails
    """
    try:
        # Parse the original S3 URL to extract components
        if not original_s3_url.startswith('https://') or '.s3.' not in original_s3_url:
            logger.error(f"Invalid S3 URL format: {original_s3_url}")
            return None

        # Extract bucket name and object key from URL
        # Format: https://bucket-name.s3.region.amazonaws.com/path/to/file.mp3
        url_parts = original_s3_url.replace('https://', '').split('/')
        bucket_and_region = url_parts[0]  # bucket-name.s3.region.amazonaws.com
        object_path = '/'.join(url_parts[1:])  # path/to/file.mp3

        # Extract bucket name (everything before .s3.)
        bucket_name = bucket_and_region.split('.s3.')[0]

        # Extract region from the URL
        region_part = bucket_and_region.split('.s3.')[1].split('.amazonaws.com')[0]

        # Parse the object path to add '_crisp' to filename
        path_parts = object_path.split('/')
        filename = path_parts[-1]  # file.mp3
        directory_path = '/'.join(path_parts[:-1])  # path/to

        # Add '_crisp' to filename before extension
        name_without_ext, ext = os.path.splitext(filename)
        crisp_filename = f"{name_without_ext}_crisp{ext}"

        # Construct new object key
        if directory_path:
            new_object_key = f"{directory_path}/{crisp_filename}"
        else:
            new_object_key = crisp_filename

        logger.info(f"Original S3 URL: {original_s3_url}")
        logger.info(f"Bucket: {bucket_name}, Region: {region_part}")
        logger.info(f"Original object key: {object_path}")
        logger.info(f"New object key: {new_object_key}")

        # Initialize S3 service and upload
        s3_service = S3Service()
        if not s3_service.s3_client:
            logger.error("S3 client not available for upload")
            return None

        # Upload the denoised file
        logger.info(f"Uploading denoised file to S3: {new_object_key}")
        s3_service.s3_client.upload_file(denoised_file_path, bucket_name, new_object_key)

        # Construct the new S3 URL
        new_s3_url = f"https://{bucket_name}.s3.{region_part}.amazonaws.com/{new_object_key}"
        logger.info(f"Successfully uploaded denoised file to: {new_s3_url}")

        return new_s3_url

    except Exception as e:
        logger.error(f"Failed to upload denoised file to S3: {str(e)}")
        return None

def denoise_audio(input_path: str, output_path: str = None) -> str:
    """
    Apply noise reduction to an audio file using FFmpeg's afftdn filter.

    Args:
        input_path: Path to the input audio file
        output_path: Path for the output file (optional, will create temp file if not provided)

    Returns:
        Path to the denoised audio file
    """
    try:
        if output_path is None:
            # Create a temporary file for the denoised audio
            import tempfile
            import os
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            file_ext = os.path.splitext(input_path)[1]
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_denoised{file_ext}", dir=os.path.dirname(input_path)) as temp_file:
                output_path = temp_file.name

        logger.info(f"Applying noise reduction to: {input_path}")
        logger.info(f"Output will be saved to: {output_path}")

        # FFmpeg command for noise reduction using afftdn (adaptive FFT denoiser)
        # This filter reduces stationary noise
        ffmpeg_cmd = [
            FFMPEG_PATH,
            "-i", input_path,
            "-af", "afftdn=nf=-25:nt=w",  # nf=-25dB noise floor, nt=w for white noise
            "-acodec", "libmp3lame",  # Use MP3 codec for output
            "-b:a", "128k",  # 128kbps bitrate
            "-y",  # Overwrite output file
            output_path
        ]

        logger.info(f"Running FFmpeg command: {' '.join(ffmpeg_cmd)}")
        result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, check=True)

        # Check if output file was created and has content
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logger.info(f"Noise reduction completed successfully. Output size: {os.path.getsize(output_path)} bytes")
            return output_path
        else:
            logger.error("Noise reduction failed - output file is empty or doesn't exist")
            return input_path  # Return original file if denoising fails

    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg noise reduction failed: {e.stderr}")
        logger.warning("Falling back to original audio file")
        return input_path  # Return original file if denoising fails
    except Exception as e:
        logger.error(f"Error during noise reduction: {str(e)}")
        logger.warning("Falling back to original audio file")
        return input_path  # Return original file if denoising fails


def detect_language(text: str) -> str:
    """Detect the language of a text using langdetect.

    Args:
        text: The text to detect the language of

    Returns:
        The detected language code (e.g., 'en', 'hi')
    """
    if not text or not text.strip():
        return "en"

    try:
        if LANGDETECT_AVAILABLE:
            detected_lang = detect(text)
            logger.info(f"Detected language: {detected_lang}")
            return detected_lang
    except Exception as e:
        logger.warning(f"Language detection failed: {str(e)}")

    # Default to English if detection fails
    return "en"


# Translation functions removed - now handled by OpenAI Whisper translations endpoint

def translate_to_english(text: str, source_lang: str) -> str:
    """
    Placeholder function for backward compatibility.
    Translation is now handled by OpenAI Whisper translations endpoint.

    Args:
        text: The text (already in English from OpenAI Whisper)
        source_lang: The source language code (unused)

    Returns:
        The text as-is (already translated by OpenAI Whisper)
    """
    # Since we're using OpenAI Whisper translations endpoint,
    # the text is already in English
    return text


# Real estate summary generation using Gemini AI
def generate_real_estate_summary(text: str, gemini_model=None) -> str:
    """
    Generate a concise, point-wise summary for real estate conversations using Gemini.
    Dynamically identifies real estate elements and adjusts the prompt accordingly.

    Args:
        text: The transcribed and translated text
        gemini_model: An initialized google.generativeai.GenerativeModel object (optional)

    Returns:
        A formatted summary with bullet points
    """
    if not text or len(text.strip()) < 10:
        return "- Insufficient content to generate a summary."

    # Clean the text
    text = re.sub(r"\b(Can you hear me|Yes, I can hear you|Tell me|Okay, sir|What are you doing|Right now|Sir|Okay)\b", "", text, flags=re.IGNORECASE)
    text = re.sub(r"\b(um+|uh+|like|hmm+|yeah+)\b", "", text, flags=re.IGNORECASE)

    # Remove repeated lines
    lines = list(dict.fromkeys(text.splitlines()))
    text = " ".join(lines)
    text = re.sub(r"\s+", " ", text).strip()

    # Dynamically analyze the text to identify real estate elements
    detected_elements = _analyze_real_estate_elements(text)

    # Generate dynamic prompt based on detected elements
    prompt = _generate_dynamic_prompt(text, detected_elements)

    try:
        # Check if a Gemini model was provided
        if gemini_model is None:
            # Try to import and initialize the model if not provided
            try:
                import google.generativeai as genai
                from config.settings import settings

                # Configure the Gemini API using the API key from settings
                genai.configure(api_key=settings.GENAI_API_KEY)

                # Initialize the model
                gemini_model = genai.GenerativeModel('gemini-1.5-flash-latest')
                logger.info("Successfully initialized Gemini model with gemini-1.5-flash-latest")
            except Exception as init_error:
                logger.error(f"Failed to initialize Gemini model: {str(init_error)}")
                logger.error("DEBUG: Triggering fallback due to INIT error.")
                return fallback_summarization(text)

        # Call Gemini with the prompt
        generation_config = {
        "temperature": 0.7,  
        "top_p": 0.95,       
        "top_k": 40,         
        "max_output_tokens": 2048 
        }

        response = gemini_model.generate_content(prompt, generation_config=generation_config)
        logger.info(f"DEBUG: Raw Gemini Response = {response}")

        # Extract text from the response
        if hasattr(response, 'candidates') and response.candidates:
            if hasattr(response.candidates[0], 'content') and response.candidates[0].content:
                if hasattr(response.candidates[0].content, 'parts') and response.candidates[0].content.parts:
                    summary = response.candidates[0].content.parts[0].text.strip()
                else:
                    logger.warning("No parts found in Gemini response content")
                    summary = ""
            else:
                logger.warning("No content found in Gemini response candidate")
                summary = ""
        elif hasattr(response, 'text'):
            # Fallback to response.text if available
            summary = response.text.strip()
        else:
            logger.warning("Unexpected Gemini response format")
            summary = ""

        # Handle empty response
        if not summary or len(summary) < 10:
            logger.warning("Gemini response was too short or empty")
            logger.warning("DEBUG: Triggering fallback due to SHORT/EMPTY summary.")
            return fallback_summarization(text)

        return summary

    except Exception as e:
        logger.error(f"Error while calling Gemini for summarization: {str(e)}")
        logger.error(f"DEBUG: Triggering fallback due to GENERAL error: {e}")
        return fallback_summarization(text)

def preprocess_text(text: str) -> str:
    """
    Preprocess the text to clean it up for summarization.

    Args:
        text: The raw transcribed text

    Returns:
        Cleaned and preprocessed text
    """
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text)

    # Remove repeated greetings and fillers
    filler_words = [
        r'\bhello\b', r'\bhi\b', r'\bhey\b', r'\bgood morning\b', r'\bgood afternoon\b',
        r'\bgood evening\b', r'\bactually\b', r'\bbasically\b', r'\bum\b', r'\buh\b',
        r'\byou know\b', r'\bi mean\b', r'\blike\b', r'\bokay\b', r'\bok\b', r'\bright\b'
    ]

    for word in filler_words:
        # Replace repeated filler words with a single instance
        text = re.sub(f'({word}\\s*)+', r'\1 ', text, flags=re.IGNORECASE)

    # Normalize punctuation
    text = re.sub(r'[.!?]+', '.', text)
    text = re.sub(r'\s*[.]\s*', '. ', text)

    # Remove any remaining excessive whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text

def create_structured_summary(text: str) -> str:
    """
    Create a structured summary of the conversation using a rule-based approach.

    Args:
        text: The preprocessed text

    Returns:
        A structured summary with bullet points
    """
    summary_points = []

    # Extract contact details (phone numbers)
    phone_pattern = r'(?:\+?[0-9]{1,4}[-\s]?)?(?:\([0-9]{1,4}\)[-\s]?)?[0-9]{3,}[-\s]?[0-9]{3,}[-\s]?[0-9]{0,}'
    phone_numbers = re.findall(phone_pattern, text)

    # Clean up phone numbers
    clean_phone_numbers = []
    for phone in phone_numbers:
        clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
        if len(clean_phone) >= 7:
            clean_phone_numbers.append(clean_phone)

    # Extract meeting/callback mentions
    meeting_patterns = [
        r'(?:call|phone|contact)(?:\s+\w+){0,3}\s+(?:back|again|later)',
        r'(?:meet|meeting|appointment)(?:\s+\w+){0,3}\s+(?:on|at|tomorrow|today|next)',
        r'(?:schedule|set\s+up)(?:\s+\w+){0,3}\s+(?:call|meeting|appointment)',
        r'(?:follow\s+up|get\s+back)(?:\s+\w+){0,5}\s+(?:on|with|to)',
        r'(?:tomorrow|today|next\s+week|next\s+month)',
        r'(?:morning|afternoon|evening)',
        r'(?:[0-9]{1,2}(?:\:[0-9]{2})?\s*(?:am|pm|AM|PM))',
        r'(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)'
    ]

    meeting_mentions = []
    for pattern in meeting_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        meeting_mentions.extend(matches)

    # Extract seller statements
    seller_patterns = [
        r'(?:I\'ll|I will|we\'ll|we will)\s+(?:arrange|set up|schedule)\s+a\s+(?:call|meeting|appointment)',
        r'(?:sales team|agent|representative|colleague)\s+(?:will|\'ll|is going to|can)\s+(?:call|contact|reach out)',
        r'(?:I\'m|I am)\s+(?:calling from|with|representing)',
        r'(?:we|I)\s+(?:have|offer|provide|sell|rent|lease)',
        r'(?:property|apartment|house|flat|villa|land|plot)'
    ]

    seller_mentions = []
    for pattern in seller_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Get the sentence containing the match
            sentences = re.split(r'[.!?]', text)
            for sentence in sentences:
                for match in matches:
                    if match in sentence:
                        seller_mentions.append(sentence.strip())

    # Extract buyer statements
    buyer_patterns = [
        r'(?:I\'m|I am)\s+(?:looking|searching|interested)',
        r'(?:I|we)\s+(?:want|need|would like|wish|hope)',
        r'(?:my|our)\s+(?:budget|requirement|preference)',
        r'(?:can you|could you|would you)\s+(?:tell|send|show|provide)',
        r'(?:how much|what is the|is there)'
    ]

    buyer_mentions = []
    for pattern in buyer_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            # Get the sentence containing the match
            sentences = re.split(r'[.!?]', text)
            for sentence in sentences:
                for match in matches:
                    if match in sentence:
                        buyer_mentions.append(sentence.strip())

    # If we couldn't identify clear buyer/seller statements, use a simpler approach
    if not seller_mentions and not buyer_mentions:
        # Split the text into sentences
        sentences = [s.strip() for s in re.split(r'[.!?]', text) if s.strip()]

        # Assume alternating speaker pattern (starting with seller)
        for i, sentence in enumerate(sentences[:4]):  # Limit to first 4 sentences
            if i % 2 == 0 and len(sentence) > 10:
                seller_mentions.append(sentence)
            elif i % 2 == 1 and len(sentence) > 10:
                buyer_mentions.append(sentence)

    # Add seller points
    if seller_mentions:
        # Remove duplicates while preserving order
        unique_seller_mentions = []
        for mention in seller_mentions:
            if mention not in unique_seller_mentions:
                unique_seller_mentions.append(mention)

        for mention in unique_seller_mentions[:2]:  # Limit to 2 points
            summary_points.append(f"- 🏢 Seller: {mention[:100]}")

    # Add buyer points
    if buyer_mentions:
        # Remove duplicates while preserving order
        unique_buyer_mentions = []
        for mention in buyer_mentions:
            if mention not in unique_buyer_mentions:
                unique_buyer_mentions.append(mention)

        for mention in unique_buyer_mentions[:2]:  # Limit to 2 points
            summary_points.append(f"- 👤 Buyer: {mention[:100]}")

    # Add contact details
    if clean_phone_numbers:
        for phone in clean_phone_numbers[:2]:  # Limit to 2 phone numbers
            summary_points.append(f"- 📞 Contact: {phone}")

    # Add meeting/callback information
    if meeting_mentions:
        unique_meeting_mentions = list(set(meeting_mentions))
        meeting_info = ", ".join(unique_meeting_mentions[:3])  # Limit to 3 mentions
        summary_points.append(f"- 📅 Meeting/Callback: {meeting_info}")

    # If no points were generated, extract key sentences
    if not summary_points:
        # Extract the first 2-3 sentences for a generic summary
        sentences = [s.strip() for s in re.split(r'[.!?]', text) if s.strip()]
        if sentences:
            # Skip greetings in the first few sentences
            filtered_sentences = []
            for sentence in sentences:
                is_greeting = False
                for greeting in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']:
                    if greeting in sentence.lower():
                        is_greeting = True
                        break
                if not is_greeting and len(sentence) > 10:
                    filtered_sentences.append(sentence)

            if filtered_sentences:
                summary = " ".join(filtered_sentences[:2])
                summary_points.append(f"- General: {summary[:150]}...")
            else:
                summary_points.append("- No clear conversation points detected.")
        else:
            summary_points.append("- No clear conversation points detected.")

    return "\n".join(summary_points)

def _analyze_real_estate_elements(text: str) -> dict:
    """
    Analyze text to identify real estate-specific elements dynamically.

    Args:
        text: The conversation text

    Returns:
        Dictionary containing detected real estate elements
    """
    text_lower = text.lower()
    elements = {
        'property_types': [],
        'configurations': [],
        'budget_mentions': [],
        'location_mentions': [],
        'area_mentions': [],
        'amenities': [],
        'inquiry_type': 'general'
    }

    # Property types
    property_patterns = {
        'apartment': r'\b(apartment|flat|unit)\b',
        'house': r'\b(house|bungalow|villa|independent house)\b',
        'land': r'\b(land|plot|site|acres?|sq\.?\s*ft|square feet)\b',
        'commercial': r'\b(office|shop|commercial|warehouse|showroom)\b',
        'pg': r'\b(pg|paying guest|hostel)\b'
    }

    for prop_type, pattern in property_patterns.items():
        if re.search(pattern, text_lower):
            elements['property_types'].append(prop_type)

    # BHK/BR configurations
    bhk_patterns = [
        r'\b(\d+)\s*(?:bhk|bedroom|br|bed)\b',
        r'\b(one|two|three|four|five|1|2|3|4|5)\s*(?:bhk|bedroom|br|bed)\b',
        r'\b(\d+)\s*(?:room|hall|kitchen)\b'
    ]

    for pattern in bhk_patterns:
        matches = re.findall(pattern, text_lower)
        for match in matches:
            if match not in elements['configurations']:
                elements['configurations'].append(match)

    # Budget mentions
    budget_patterns = [
        r'\b(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:lakh|lakhs?|crore|crores?)\b',
        r'\b(?:budget|price|cost|amount)\s*(?:is|of|around|approximately)?\s*(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:lakh|lakhs?|crore|crores?)\b',
        r'\b(\d+(?:,\d+)*)\s*(?:rupees?|rs\.?|inr)\b'
    ]

    for pattern in budget_patterns:
        matches = re.findall(pattern, text_lower)
        elements['budget_mentions'].extend(matches)

    # Location mentions
    location_patterns = [
        r'\b(bangalore|mumbai|delhi|pune|hyderabad|chennai|kolkata|ahmedabad|gurgaon|noida)\b',
        r'\b(sector|phase|block|area|locality|near|close to)\s+([a-zA-Z\s]+)\b',
        r'\b(pin\s*code|pincode)\s*(\d{6})\b'
    ]

    for pattern in location_patterns:
        matches = re.findall(pattern, text_lower)
        for match in matches:
            if isinstance(match, tuple):
                elements['location_mentions'].extend([m for m in match if m.strip()])
            else:
                elements['location_mentions'].append(match)

    # Area/size mentions
    area_patterns = [
        r'\b(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:sq\.?\s*ft|square\s*feet|sqft)\b',
        r'\b(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:acres?|hectares?)\b',
        r'\b(carpet\s*area|built\s*up\s*area|super\s*built\s*up)\s*(?:of|is)?\s*(\d+(?:,\d+)*(?:\.\d+)?)\b'
    ]

    for pattern in area_patterns:
        matches = re.findall(pattern, text_lower)
        for match in matches:
            if isinstance(match, tuple):
                elements['area_mentions'].extend([m for m in match if m.strip() and m.replace(',', '').replace('.', '').isdigit()])
            else:
                elements['area_mentions'].append(match)

    # Amenities
    amenity_patterns = [
        r'\b(parking|gym|swimming\s*pool|club\s*house|security|lift|elevator)\b',
        r'\b(garden|park|playground|tennis\s*court|badminton\s*court)\b',
        r'\b(power\s*backup|generator|water\s*supply|bore\s*well)\b'
    ]

    for pattern in amenity_patterns:
        matches = re.findall(pattern, text_lower)
        elements['amenities'].extend(matches)

    # Inquiry type
    if any(word in text_lower for word in ['buy', 'purchase', 'buying']):
        elements['inquiry_type'] = 'buying'
    elif any(word in text_lower for word in ['rent', 'rental', 'lease']):
        elements['inquiry_type'] = 'rental'
    elif any(word in text_lower for word in ['sell', 'selling', 'sale']):
        elements['inquiry_type'] = 'selling'
    elif any(word in text_lower for word in ['invest', 'investment']):
        elements['inquiry_type'] = 'investment'

    return elements

def _generate_dynamic_prompt(text: str, elements: dict) -> str:
    """
    Generate a dynamic prompt based on detected real estate elements.

    Args:
        text: The conversation text
        elements: Detected real estate elements

    Returns:
        Customized prompt string
    """
    base_prompt = """You are an expert real estate conversation analyst. Your task is to analyze the following real estate conversation in detail and extract ALL key information into a structured format. Be thorough and capture all relevant facts, figures, and expressed needs."""

    # Build specific instructions based on detected elements
    specific_instructions = []

    if elements['property_types']:
        prop_types = ', '.join(elements['property_types'])
        specific_instructions.append(f"- Property Type: Explicitly identify all property types mentioned, focusing on '{prop_types}' details.")

    if elements['configurations']:
        configs = ', '.join(set(elements['configurations']))
        specific_instructions.append(f"- Configuration: Extract all details related to property configurations, such as '{configs}' or specific bedroom/hall/bathroom counts.")

    if elements['budget_mentions']:
        specific_instructions.append("- Budget: Accurately identify and extract all price, budget, and financial discussions, including exact amounts, ranges, or any related monetary terms.")
    else:
        specific_instructions.append("- Budget: Note if budget information is sought or discussed, even if specific amounts aren't provided.")


    if elements['location_mentions']:
        specific_instructions.append("- Location: Extract ALL specific areas, localities, neighborhoods, and broader geographical references (e.g., 'downtown').")

    if elements['area_mentions']:
        specific_instructions.append("- Area/Size: Capture all details regarding property area or size, including carpet area, built-up area, plot size, or any dimensions mentioned, along with units.")

    if elements['amenities']:
        amenities = ', '.join(set(elements['amenities']))
        specific_instructions.append(f"- Amenities: List all mentioned amenities, facilities, views (e.g., 'view'), or specific features requested or offered, with a focus on '{amenities}'.")
    else:
        specific_instructions.append("- Amenities: Note if amenities are discussed or if preferences are being sought, even if specific ones aren't listed.")


    # Inquiry type specific instructions
    inquiry_instructions = {
        'buying': "- Transaction Type: This is a BUYING inquiry. Focus on the buyer's purchase requirements, preferences, and any offers or conditions related to buying.",
        'rental': "- Transaction Type: This is a RENTAL inquiry. Focus on lease terms, monthly rent, duration, and specific rental property needs.",
        'selling': "- Transaction Type: This is a SELLING inquiry. Focus on the property details being offered for sale, desired price, and any selling conditions.",
        'investment': "- Transaction Type: This is an INVESTMENT inquiry. Focus on return on investment (ROI), potential for appreciation, and investment criteria.",
        'general': "- Transaction Type: Analyze the conversation to DETERMINE if this is a buying, selling, renting, or investment inquiry. Clearly state the identified transaction type."
    }

    specific_instructions.append(inquiry_instructions[elements['inquiry_type']])

    # Add instructions for identifying roles and next steps more explicitly
    specific_instructions.append("- Roles: Clearly distinguish between the 'Buyer' (the party looking to acquire property) and the 'Seller' (the party offering property).")
    specific_instructions.append("- Next Steps: Identify all concrete next steps, actions, or commitments, such as meetings, site visits, follow-ups, information sharing (e.g., via WhatsApp), or scheduled callbacks.")
    specific_instructions.append("- Contact: Extract any explicitly mentioned phone numbers or contact details.")


    # Format instructions - significantly improved for clarity and completeness
    format_instructions = """
        Extract all identified information using the following exact format. If a section has no relevant information, explicitly state "N/A" for that section instead of skipping it.

        👤 **Buyer:** [Detailed description of the buyer's requirements, preferences, questions asked, and any specific needs. Include their name if mentioned.]
        🏢 **Seller:** [Detailed description of the seller's offerings, property details provided, questions asked by seller, and any proposed actions. Include their name if mentioned.]
        🏠 **Property Details:** [Type, configuration (e.g., 2BHK), exact area/size with units, precise location/locality, and a comprehensive list of amenities or features mentioned.]
        💰 **Budget/Financials:** [All monetary discussions, exact prices, budget ranges, offers, or financial terms. State if budget information is being sought or is pending.]
        📅 **Next Steps:** [All concrete, actionable next steps, including scheduled meetings, site visits with date/time, follow-ups, information sharing methods (e.g., WhatsApp), or callbacks.]
        📞 **Contact:** [Any explicit phone numbers, email addresses, or specific contact instructions.]
        ---
        **Transaction Type:** [State whether the inquiry is 'Buying', 'Selling', 'Rental', 'Investment', or 'Undetermined'.]

        Guidelines for Extraction:
        - **Completeness:** Strive to extract every piece of relevant information for each category.
        - **Accuracy:** Use exact phrases, numbers, and amounts directly from the conversation where possible.
        - **Conciseness:** While complete, keep descriptions clear and to the point without adding interpretation.
        - **Explicit Mention:** Only include information that is explicitly stated or strongly implied in the conversation. Do not infer or assume.
        - **No Fillers:** Avoid conversational fillers or pleasantries in the extracted output.
        - **Maximum Points:** Provide a comprehensive summary within these structured headings. Aim for 6-8 key points in total across all sections, where each section itself is a point.
        """

    # Combine all parts
    full_prompt = f"""{base_prompt}

    Specific Focus Areas:
    {chr(10).join(specific_instructions)}

    {format_instructions}

    Conversation Transcript:
    \"\"\"
    {text}
    \"\"\"
    """

        return full_prompt

def fallback_summarization(text: str) -> str:
    """
    Fallback method for summarization when the primary approach fails.
    Uses a simplified rule-based approach to extract key information.

    Args:
        text: The transcribed and translated text

    Returns:
        A formatted summary with bullet points
    """
    # Clean the text
    text = re.sub(r'\s+', ' ', text).strip()

    # Extract phone numbers using regex
    phone_pattern = r'(?:\+?[0-9]{1,4}[-\s]?)?(?:\([0-9]{1,4}\)[-\s]?)?[0-9]{3,}[-\s]?[0-9]{3,}[-\s]?[0-9]{0,}'
    phone_numbers = re.findall(phone_pattern, text)

    # Clean up phone numbers
    clean_phone_numbers = []
    for phone in phone_numbers:
        clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
        if len(clean_phone) >= 7:
            clean_phone_numbers.append(clean_phone)

    # Extract meeting mentions
    meeting_pattern = r'(?:call|meet|appointment|schedule|tomorrow|today|next week|[0-9]{1,2}(?:\:[0-9]{2})?\s*(?:am|pm))'
    meeting_mentions = re.findall(meeting_pattern, text, re.IGNORECASE)

    # Create a basic summary
    summary_points = []

    # Split into sentences and filter out greetings and very short sentences
    sentences = []
    for s in re.split(r'[.!?]', text):
        s = s.strip()
        if s and len(s) > 5:
            # Skip if it's just a greeting
            is_greeting = False
            for greeting in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']:
                if s.lower().startswith(greeting):
                    is_greeting = True
                    break
            if not is_greeting:
                sentences.append(s)

    # Try to identify seller and buyer statements
    if sentences:
        # Assume first non-greeting sentence is from seller
        if len(sentences) > 0:
            summary_points.append(f"- 🏢 Seller: {sentences[0][:100]}")

        # Assume second non-greeting sentence is from buyer
        if len(sentences) > 1:
            summary_points.append(f"- 👤 Buyer: {sentences[1][:100]}")

    # Add contact details
    if clean_phone_numbers:
        for phone in clean_phone_numbers[:2]:
            summary_points.append(f"- 📞 Contact: {phone}")

    # Add meeting information
    if meeting_mentions:
        meeting_info = ", ".join(list(set(meeting_mentions))[:3])
        summary_points.append(f"- 📅 Meeting/Callback: {meeting_info}")

    # If no points were generated, add a generic message
    if not summary_points:
        summary_points.append("- No clear conversation points detected.")

    return "\n".join(summary_points)
