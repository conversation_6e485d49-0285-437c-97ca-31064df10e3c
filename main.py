import os
import logging
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from Modules.Document_Processor.app import router as document_router
from Modules.Transcriber.app import router as transcriber_router
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Set specific loggers to DEBUG
logging.getLogger("Modules.Transcriber.google_stt").setLevel(logging.DEBUG)
logging.getLogger("Modules.Transcriber.api").setLevel(logging.DEBUG)
logging.getLogger("Modules.Transcriber.services").setLevel(logging.DEBUG)

# Disable some noisy loggers
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("google").setLevel(logging.INFO)

# Translation routers disabled
TRANSLATION_API_AVAILABLE = False
TRANSLATE_API_AVAILABLE = False

# Check if running in Cloud Run
IN_CLOUD_RUN = os.environ.get("K_SERVICE") is not None
if IN_CLOUD_RUN:
    logger.info("Running in Google Cloud Run environment")
    logger.info(f"Using port {os.environ.get('PORT', 8080)}")

# Initialize FastAPI app
app = FastAPI(
    title="Document Processing API",
    description="API for processing documents, extracting images, and analyzing content",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(document_router, prefix="/documents", tags=["Documents"])
app.include_router(transcriber_router, prefix="/transcriber", tags=["Transcriber"])

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to the Document Processing API",
        "controllers": {
            "Documents": {
                "endpoints": {
                    "split_images": "/documents/split_images",
                    "process": "/documents/process"
                }
            },
            "Transcriber": {
                "endpoints": {
                    "transcribe": "/transcriber/transcribe/",
                    "docs": "/transcriber/docs"
                }
            },
            "Translation": {
                "available": False,
                "endpoints": {},
                "note": "Translation APIs are currently disabled"
            },
            "API Translation": {
                "available": False,
                "endpoints": {},
                "note": "Translation APIs are currently disabled"
            }
        }
    }

# Entrypoint
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Run the Document Processing API')
    parser.add_argument('--port', type=int, help='Port to run the server on (overrides env PORT)')
    args = parser.parse_args()

    # Prefer CLI --port, then fall back to Cloud Run's PORT env var
    port = args.port if args.port else int(os.environ.get("PORT", 8080))

    print(f"Starting server on port {port}")
    print(f"Environment PORT: {os.environ.get('PORT', 'not set')}")
    print(f"Current directory: {os.getcwd()}")
    print(f"Files in current directory: {os.listdir('.')}")

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,  # Use the port variable instead of hardcoded value
        workers=1,  # Reduced workers for better stability
        log_level="info",
        timeout_keep_alive=60
    )

