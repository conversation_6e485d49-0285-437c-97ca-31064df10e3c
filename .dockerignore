# Git
.git
.gitignore

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Python virtual environments
venv/
ENV/
env/
.Python

# Python packaging artifacts
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# IDE settings
.idea/
.vscode/
*.swp
*.swo

# Docker configuration (exclude Dockerfile, keep .dockerignore)
docker-compose.yml

# Logs and debugging
logs/
*.log

# Environment and secrets (keep your package.json files)
.env
.env.*
*.json
!package.json
!package-lock.json

# Audio files (to reduce build context size)
*.wav
*.mp3
*.m4a
*.flac
*.ogg
*.aac

# Cache and local tools
*.db
*.sqlite3
.cache/
__pypackages__/
