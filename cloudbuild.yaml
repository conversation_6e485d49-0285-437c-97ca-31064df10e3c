steps:
# Build the container image
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'gcr.io/$PROJECT_ID/leadrat-black-ai', '.']

# Push the container image to Container Registry
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'gcr.io/$PROJECT_ID/leadrat-black-ai']

# Deploy container image to Cloud Run
- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  entrypoint: gcloud
  args:
  - 'run'
  - 'deploy'
  - 'leadrat-black-ai'
  - '--image'
  - 'gcr.io/$PROJECT_ID/leadrat-black-ai'
  - '--region'
  - 'asia-south1'
  - '--platform'
  - 'managed'
  - '--allow-unauthenticated'
  - '--memory'
  - '2Gi'
  - '--cpu'
  - '1'
  - '--timeout'
  - '300s'
  - '--set-env-vars'
  - 'GCP_PROJECT_ID=$PROJECT_ID'

images:
- 'gcr.io/$PROJECT_ID/leadrat-black-ai'
