# Introduction
TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project.

# Getting Started
TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:
1.	Installation process
2.	Software dependencies
3.	Latest releases
4.	API references

# Build and Test
TODO: Describe and show how to build your code and run the tests.

# Contribute
TODO: Explain how other users and developers can contribute to make your code better.

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)

# Leadrat Black AI

A document processing and transcription service that uses OpenAI Whisper API, AWS S3, and Google AI services.

## Features

- **Audio Transcription with OpenAI Whisper API**
  - Transcribes audio in any language and returns results in English only
  - Support for various audio formats (MP3, WAV, M4A, FLAC, OGG, AAC)
  - Process audio files via direct upload or S3 URL
  - High-quality transcription with automatic language detection
- **Document Processing**
  - PDF document processing and image extraction
  - AI-powered document analysis using Google Gemini
- **Cloud Storage Integration**
  - AWS S3 for file storage and processing
- **Real Estate Conversation Summaries**
  - AI-generated summaries of real estate conversations using Google Gemini

## Docker Setup (Local Development)

### Prerequisites

- Docker and Docker Compose installed on your system
- AWS and Google Cloud credentials configured in `.env` file

### Running with Docker Locally

1. Make sure your `.env` file is properly configured with all required credentials:

```
# API Configuration
API_HOST=0.0.0.0
API_PORT=8080

# AWS Configuration
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# S3 Configuration
S3_BUCKET_NAME=leadrat-black
S3_BASE_FOLDER=documents/
S3_SUBFOLDER=uploads/

# Google Cloud Storage Configuration
GCS_BUCKET_NAME=leadrat-black-gcs
GCS_FOLDER_NAME=audio/

# Google AI Configuration
GENAI_API_KEY=your_google_ai_api_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Google Cloud Project ID (for Secret Manager)
GCP_PROJECT_ID=your-gcp-project-id

# Development Settings
DEBUG=true
ENVIRONMENT=development
```

2. Build and start the Docker container:

```bash
docker-compose up -d
```

3. The API will be available at http://localhost:8080

4. To view logs:

```bash
docker-compose logs -f
```

5. To stop the container:

```bash
docker-compose down
```

## Google Cloud Run Deployment

### Prerequisites

- Google Cloud SDK (gcloud) installed and configured
- Docker installed on your system
- A Google Cloud project with the following APIs enabled:
  - Cloud Run
  - Secret Manager
  - Cloud Storage
  - Cloud Speech-to-Text
  - Cloud Translation

### Deployment Steps

1. Set up your environment variables:

```bash
export GCP_PROJECT_ID=your-gcp-project-id
export SERVICE_NAME=leadrat-black-ai
export REGION=us-central1
```

2. (Optional) Set up secrets in your local environment to be uploaded to Secret Manager:

```bash
export AWS_ACCESS_KEY_ID=your_aws_access_key
export AWS_SECRET_ACCESS_KEY=your_aws_secret_key
export GENAI_API_KEY=your_google_ai_api_key
```

3. Run the deployment script:

```bash
chmod +x deploy-cloud-run.sh
./deploy-cloud-run.sh
```

4. The script will:
   - Build and push the Docker image to Google Container Registry
   - Create or update secrets in Secret Manager
   - Deploy the application to Cloud Run
   - Grant the Cloud Run service account access to Secret Manager
   - Output the URL of your deployed service

### Manual Deployment

If you prefer to deploy manually:

1. Build and push the Docker image:

```bash
docker build -t gcr.io/your-project-id/leadrat-black-ai .
docker push gcr.io/your-project-id/leadrat-black-ai
```

2. Deploy to Cloud Run:

```bash
gcloud run deploy leadrat-black-ai \
  --image gcr.io/your-project-id/leadrat-black-ai \
  --platform managed \
  --region us-central1 \
  --set-env-vars "GCP_PROJECT_ID=your-project-id" \
  --allow-unauthenticated
```

## Environment Variables in Cloud Run

When deploying to Cloud Run, you can set environment variables directly in the Cloud Run service configuration or use Secret Manager for sensitive values.

Required environment variables:
- `GCP_PROJECT_ID`: Your Google Cloud project ID

Optional environment variables:
- `PORT`: The port the server will listen on (defaults to 8080)
- `AWS_REGION`: AWS region (defaults to ap-south-1)
- `S3_BUCKET_NAME`: AWS S3 bucket name (defaults to leadrat-black)
- `GCS_BUCKET_NAME`: Google Cloud Storage bucket name (defaults to leadrat-black-gcs)
- `DEBUG`: Set to "true" to enable debug logging (defaults to "false")

Secrets (stored in Secret Manager):
- `aws-access-key-id`: AWS access key ID
- `aws-secret-access-key`: AWS secret access key
- `genai-api-key`: Google AI API key

## API Endpoints

- `GET /`: Welcome message and API information
- `GET /transcriber/`: Transcriber API information
- `POST /transcriber/transcribe/`: Upload and transcribe audio files using OpenAI Whisper API
  - Supports file upload or S3 URL
  - Returns transcription in English regardless of original language
  - Includes AI-generated real estate conversation summary
- `GET /docs`: Interactive API documentation (Swagger UI)

## Development

For local development without Docker:

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Run the application:

```bash
uvicorn main:app --host 0.0.0.0 --port 8080 --reload
```