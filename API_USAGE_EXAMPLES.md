# Transcribe API Usage Examples

## Endpoint
`POST /transcriber/transcribe/`

## Usage Patterns

### 1. Using audio_url parameter (NEW - Now Working!)

#### Regular HTTP/HTTPS URLs
```bash
curl -X POST "http://localhost:8000/transcriber/transcribe/" \
  -F "audio_url=https://example.com/audio.mp3"
```

#### S3 URLs (Multiple formats supported)
```bash
# Format 1: Standard S3 URL
curl -X POST "http://localhost:8000/transcriber/transcribe/" \
  -F "audio_url=https://my-bucket.s3.us-east-1.amazonaws.com/path/to/audio.mp3"

# Format 2: S3 protocol URL
curl -X POST "http://localhost:8000/transcriber/transcribe/" \
  -F "audio_url=s3://my-bucket/path/to/audio.mp3"

# Format 3: Legacy S3 URL format
curl -X POST "http://localhost:8000/transcriber/transcribe/" \
  -F "audio_url=https://s3.amazonaws.com/my-bucket/path/to/audio.mp3"
```

### 2. Using file upload (Existing functionality - Unchanged)
```bash
curl -X POST "http://localhost:8000/transcriber/transcribe/" \
  -F "file=@/path/to/local/audio.mp3"
```

### 3. Python Examples

#### Using audio_url
```python
import requests

# Regular URL
response = requests.post(
    "http://localhost:8000/transcriber/transcribe/",
    data={"audio_url": "https://example.com/audio.mp3"}
)

# S3 URL
response = requests.post(
    "http://localhost:8000/transcriber/transcribe/",
    data={"audio_url": "https://my-bucket.s3.us-east-1.amazonaws.com/audio.mp3"}
)
```

#### Using file upload
```python
import requests

with open("audio.mp3", "rb") as f:
    response = requests.post(
        "http://localhost:8000/transcriber/transcribe/",
        files={"file": f}
    )
```

## Response Format
```json
{
  "original_text": "Transcribed text in English",
  "translated_text": "Same as original (already in English)",
  "language_detected": "en",
  "summary": "Generated summary of the conversation",
  "error": null
}
```

## Error Handling
- If neither `audio_url` nor `file` is provided: Returns error
- If URL is inaccessible: Returns error with details
- If S3 access fails: Attempts HTTP fallback, then returns error if both fail
- If file is empty or corrupted: Returns appropriate error message

## Supported Audio Formats
- MP3, WAV, M4A, OGG, FLAC
- Maximum file size: 25MB (OpenAI Whisper limit)
- Automatic format detection from URL or Content-Type headers
