import os
import tempfile
import logging
import asyncio
import boto3
import uuid
from typing import List, Dict, Any, <PERSON><PERSON>, Optional
from datetime import datetime
from fastapi import APIRouter, UploadFile, HTTPException
from pydantic import BaseModel
import google.generativeai as genai
from config.settings import settings

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Configure Gemini with API key from settings
genai.configure(api_key=settings.GENAI_API_KEY)
model = genai.GenerativeModel('gemini-pro-vision')

# Pydantic Models
class DocumentResponse(BaseModel):
    """Response model for document processing results."""
    documentId: str
    images: Dict[str, Any]
    location: Optional[str] = None
    lastModifiedOn: Optional[str] = None
    createdOn: Optional[str] = None

    # Allow additional fields from payload
    class Config:
        extra = "allow"

# S3 Service
class S3Service:
    def __init__(self):
        logger.debug(f"Initializing S3Service with region: {settings.AWS_REGION}")
        logger.debug(f"Using bucket: {settings.S3_BUCKET_NAME}")
        logger.debug(f"AWS Access Key ID: {settings.AWS_ACCESS_KEY_ID[:4]}..." if settings.AWS_ACCESS_KEY_ID else "No AWS Access Key ID")

        self.s3_client = boto3.client("s3",
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        self.bucket_name = settings.S3_BUCKET_NAME
        self.base_folder = settings.S3_BASE_FOLDER
        self.temp_folder = settings.S3_SUBFOLDER

    def save_file_to_temp(self, file_path: str, folder: str, filename: str) -> str:
        try:
            s3_key = f"{self.base_folder}/{folder}/{filename}"
            logger.debug(f"Attempting upload with: Bucket={self.bucket_name}, Key={s3_key}")
            logger.debug(f"Using region: {settings.AWS_REGION}")
            logger.debug(f"File exists: {os.path.exists(file_path)}")

            self.s3_client.upload_file(file_path, self.bucket_name, s3_key)
            logger.debug("File upload successful")

            url = f"https://{self.bucket_name}.s3.{settings.AWS_REGION}.amazonaws.com/{s3_key}"
            logger.debug(f"Generated S3 URL: {url}")
            return url

        except Exception as e:
            logger.error(f"Failed to upload file to S3: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to upload file to S3: {str(e)}")

    async def save_upload_to_temp(self, file: UploadFile, folder: str) -> str:
        try:
            s3_key = f"{self.base_folder}/{folder}/{file.filename}"
            logger.debug(f"Attempting to upload file to S3 key: {s3_key}")

            await file.seek(0)
            logger.debug("File seek complete, starting upload...")

            try:
                logger.debug("Testing S3 connection...")
                self.s3_client.head_bucket(Bucket=self.bucket_name)
                logger.debug("S3 connection test successful")
            except Exception as e:
                logger.error(f"S3 connection test failed: {str(e)}")
                raise HTTPException(status_code=500, detail=f"S3 connection failed: {str(e)}")

            self.s3_client.upload_fileobj(file.file, self.bucket_name, s3_key)
            logger.debug("File upload successful")

            url = f"https://{self.bucket_name}.s3.{settings.AWS_REGION}.amazonaws.com/{s3_key}"
            logger.debug(f"Generated S3 URL: {url}")
            return url

        except Exception as e:
            logger.error(f"Failed to upload file to S3: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to upload file to S3: {str(e)}")

    def download_from_temp(self, s3_url: str) -> str:
        try:
            s3_key = s3_url.replace(f"https://{self.bucket_name}.s3.{settings.AWS_REGION}.amazonaws.com/", "")

            temp_file = tempfile.NamedTemporaryFile(delete=False)
            temp_path = temp_file.name
            temp_file.close()

            self.s3_client.download_file(self.bucket_name, s3_key, temp_path)

            return temp_path
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to download file from S3: {str(e)}")

# Image Service
class ImageService:
    def __init__(self):
        self.batch_size = 5
        self.cache = {}

    async def analyze_image_batch(self, image_paths: List[str], payload: Dict[str, Any] = None) -> Dict[str, Any]:
        try:
            results = {}
            for i in range(0, len(image_paths), self.batch_size):
                batch = image_paths[i:i + self.batch_size]
                batch_results = await asyncio.gather(
                    *[self._process_single_image(path, payload) for path in batch],
                    return_exceptions=True
                )

                for path, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        logger.error(f"Failed to process image {path}: {str(result)}")
                        results[path] = {"error": str(result)}
                    else:
                        results[path] = result

            return results

        except Exception as e:
            logger.error(f"Failed to process image batch: {str(e)}")
            raise

    async def _process_single_image(self, image_path: str, payload: Dict[str, Any] = None) -> Dict[str, Any]:
        try:
            if image_path in self.cache:
                logger.debug(f"Using cached results for {image_path}")
                return self.cache[image_path]

            s3_service = S3Service()
            local_path = s3_service.download_from_temp(image_path)

            result = await self._analyze_image(local_path, payload)

            os.unlink(local_path)

            self.cache[image_path] = result

            return result

        except Exception as e:
            logger.error(f"Failed to process image {image_path}: {str(e)}")
            raise

    async def _analyze_image(self, image_path: str, payload: Dict[str, Any] = None) -> Dict[str, Any]:
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # Generate dynamic prompt based on payload
            prompt = self._generate_analysis_prompt(payload)

            response = await model.generate_content_async([prompt, image_data])

            if not response.text:
                logger.warning("Empty response from Gemini, using fallback prompt")
                fallback_prompt = "Describe this image in detail, including any text, objects, people, or scenes visible."
                response = await model.generate_content_async([fallback_prompt, image_data])

            analysis = self._process_response(response.text)

            return {
                "analysis": analysis,
                "timestamp": datetime.utcnow().isoformat(),
                "model": "gemini-pro-vision"
            }

        except Exception as e:
            logger.error(f"Failed to analyze image: {str(e)}")
            raise

    def _generate_analysis_prompt(self, payload: Dict[str, Any] = None) -> str:
        """Generate a dynamic prompt based on the payload fields."""
        # Base prompt that works for any property
        base_prompt = """
        Analyze this image in detail and provide information about:
        1. Property Features:
           - Type of property
           - Size and layout
           - Condition and quality
           - Notable architectural features

        2. Amenities:
           - Available facilities
           - Special features
           - Outdoor spaces

        3. Location Details:
           - Setting and surroundings
           - Accessibility
           - Notable landmarks

        4. Additional Information:
           - Any visible text or signage
           - People or activities shown
           - Time of day or lighting conditions
        """

        # If no payload, return the base prompt
        if not payload:
            return base_prompt

        # Add specific instructions based on payload fields
        additional_instructions = []

        # Check for specific fields in payload and add relevant instructions
        if "projectType" in payload:
            additional_instructions.append("Focus on identifying the property type (residential, commercial, etc.) and any subtypes.")

        if "projectAmenities" in payload:
            additional_instructions.append("Pay special attention to amenities like pools, gyms, parking, security, etc.")

        if "address" in payload:
            additional_instructions.append("Look for location information including city, state, district, locality, or postal code.")

        if "totalFlats" in payload or "totalBlocks" in payload or "totalFloor" in payload:
            additional_instructions.append("Identify information about the number of flats, blocks, or floors if visible.")

        if "area" in payload:
            additional_instructions.append("Look for information about the property area or size.")

        # Add any additional instructions to the base prompt
        if additional_instructions:
            return base_prompt + "\n\nAdditional focus areas:\n" + "\n".join(f"- {instruction}" for instruction in additional_instructions)

        return base_prompt

    def _process_response(self, response_text: str) -> Dict[str, Any]:
        return {
            "raw_text": response_text,
            "timestamp": datetime.utcnow().isoformat()
        }

# Document Service
class DocumentService:
    def __init__(self):
        self.s3_service = S3Service()
        self.image_service = ImageService()

    def extract_images(self, file_path: str, tenant_id: str) -> Tuple[List[str], str]:
        try:
            logger.debug(f"Starting image extraction for file: {file_path}")
            image_paths, doc_id = self._extract_images(file_path, tenant_id)
            logger.debug(f"Extracted {len(image_paths)} images with document ID: {doc_id}")
            return image_paths, doc_id
        except Exception as e:
            logger.error(f"Failed to extract images: {str(e)}")
            raise

    def _extract_images(self, file_path: str, tenant_id: str) -> Tuple[List[str], str]:
        unique_id = f"doc_{uuid.uuid4().hex[:8]}"
        temp_s3_folder = f"{tenant_id}/{unique_id}"
        s3_image_urls = []
        text_content = []

        logger.debug(f"Starting extraction for document {unique_id}")

        with tempfile.TemporaryDirectory() as temp_dir:
            filename = os.path.splitext(os.path.basename(file_path))[0]

            with fitz.open(file_path) as doc:
                for page_num, page in enumerate(doc):
                    page_text = page.get_text()
                    text_content.append(f"Page {page_num + 1}:\n{page_text}\n")
                    logger.debug(f"Extracted text from page {page_num + 1}")

                    for img_index, img in enumerate(page.get_images(full=True)):
                        xref = img[0]
                        base_image = doc.extract_image(xref)
                        temp_img_path = os.path.join(temp_dir, f"{filename}_page{page_num}_img{img_index}.png")
                        with open(temp_img_path, "wb") as img_file:
                            img_file.write(base_image["image"])
                        s3_url = self.s3_service.save_file_to_temp(temp_img_path, temp_s3_folder, f"page{page_num}_img{img_index}.png")
                        s3_image_urls.append(s3_url)
                        logger.debug(f"Saved image {img_index} from page {page_num} to S3")

                text_s3_url = self._save_text_to_s3(text_content, temp_s3_folder, unique_id)
                logger.debug(f"Saved text content to {text_s3_url}")

        logger.info(f"Extraction completed for document {unique_id} with {len(s3_image_urls)} images")
        return s3_image_urls, unique_id

    def _save_text_to_s3(self, text_content: List[str], s3_folder: str, doc_id: str) -> str:
        try:
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as temp_file:
                temp_file.write("\n".join(text_content))
                temp_file.flush()

                filename = f"{doc_id}_ChunkTextFile.txt"
                s3_url = self.s3_service.save_file_to_temp(temp_file.name, s3_folder, filename)

            os.unlink(temp_file.name)
            return s3_url

        except Exception as e:
            logger.error(f"Failed to save text content to S3: {str(e)}")
            raise Exception(f"Failed to save text content to S3: {str(e)}")

    async def process_images(self, image_paths: List[str], payload: Dict[str, Any] = None) -> DocumentResponse:
        try:
            logger.debug(f"Processing images with payload: {payload}")
            results = await self.image_service.analyze_image_batch(image_paths, payload)

            # Fix document ID generation
            doc_id = (payload or {}).get('document_id') or f"doc_{uuid.uuid4().hex[:8]}"

            # Create a base response with required fields
            response_data = {
                "documentId": doc_id,
                "images": results,
                "createdOn": datetime.utcnow().isoformat(),
                "lastModifiedOn": datetime.utcnow().isoformat()
            }

            # If payload exists, merge it with the response data
            if payload:
                # Create a copy of payload to avoid modifying the original
                payload_copy = payload.copy()

                # Remove document_id if it exists to avoid duplication
                if "document_id" in payload_copy:
                    del payload_copy["document_id"]

                # Merge payload with response data
                response_data.update(payload_copy)

            # Create the response using the DocumentResponse model
            response = DocumentResponse(**response_data)

            return response

        except Exception as e:
            logger.error(f"Failed to process images: {str(e)}")
            raise

# FastAPI Router
router = APIRouter()
document_service = DocumentService()

@router.post("/split_images")
async def split_images(tenant_id: str, file: UploadFile) -> Dict[str, Any]:
    temp_file = None
    try:
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
        content = await file.read()
        if not content:
            raise ValueError("Received empty file content")

        temp_file.write(content)
        temp_file.flush()
        temp_file.close()

        temp_path = temp_file.name
        logger.debug(f"File content written to temporary file: {temp_path}")

        image_paths, doc_id = document_service.extract_images(temp_path, tenant_id)
        logger.debug(f"Extracted {len(image_paths)} images with document ID: {doc_id}")

        try:
            os.unlink(temp_path)
        except Exception as cleanup_error:
            logger.warning(f"Failed to cleanup temporary file: {str(cleanup_error)}")

        return {
            "image_paths": image_paths,
            "document_id": doc_id
        }

    except Exception as e:
        if temp_file is not None:
            try:
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
            except Exception as cleanup_error:
                logger.warning(f"Failed to cleanup temporary file after error: {str(cleanup_error)}")

        logger.error(f"Failed to split images: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/process", response_model=DocumentResponse)
async def process_images(image_paths: List[str], payload: Dict[str, Any] = None) -> DocumentResponse:
    try:
        logger.debug(f"Processing images with payload: {payload}")
        return await document_service.process_images(image_paths, payload)
    except Exception as e:
        logger.error(f"Failed to process images: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))