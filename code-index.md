# Leadrat Black AI - Code Index

## Project Overview

Leadrat Black AI is a document processing and transcription service that integrates with AWS S3, Google Cloud Storage, and Google AI services. The application provides APIs for document processing, image extraction, audio transcription with multi-language support, and translation services.

## System Architecture

The application is built using FastAPI and is organized into modules:

1. **Main Application** - Initializes the FastAPI app and includes routers from different modules
2. **Document Processor Module** - Handles document processing and image extraction
3. **Transcriber Module** - Provides audio transcription services using Google Speech-to-Text

## Core Components

### 1. Main Application (`main.py`)

The entry point for the application that:
- Configures logging
- Initializes the FastAPI app
- Sets up CORS middleware
- Includes routers from the Document Processor and Transcriber modules
- Provides a root endpoint with API information

### 2. Configuration (`config/settings.py`)

Manages application settings using Pydantic:
- API configuration (host, port)
- AWS settings (credentials, region, S3 bucket)
- Google Cloud Storage settings
- Google AI settings
- Environment settings (debug mode, environment type)
- Secret management for Cloud Run deployment

### 3. Document Processor Module (`Modules/Document_Processor/app.py`)

Provides document processing capabilities:
- **S3Service**: Handles file uploads and downloads to/from AWS S3
- **ImageService**: Analyzes images using Google's Gemini Pro Vision model
- **DocumentService**: Extracts images from documents and processes them
- **API Endpoints**:
  - `/documents/split_images`: Extracts images from uploaded documents
  - `/documents/process`: Analyzes extracted images using AI

### 4. Transcriber Module

#### 4.1. API Layer (`Modules/Transcriber/app.py` & `Modules/Transcriber/api.py`)

- Initializes the Transcriber module and registers API endpoints
- Provides the `/transcriber/transcribe/` endpoint for audio transcription
- Handles file uploads and URL-based audio processing

#### 4.2. Services (`Modules/Transcriber/services.py`)

- **S3Service**: Handles S3 operations for audio files
- **Audio Processing**: Functions for audio format conversion and noise reduction
- **Translation**: Functions to translate transcribed text to English
- **Summarization**: Generates summaries of transcribed conversations using Google's Gemini model

#### 4.3. Google Speech-to-Text Integration (`Modules/Transcriber/google_stt.py`)

- Handles audio transcription using Google Cloud Speech-to-Text API
- Optimizes audio processing with chunking and parallel processing
- Provides language detection and translation capabilities

## Deployment Configuration

The project includes multiple deployment options:

1. **Docker Deployment**:
   - `Dockerfile`: Defines the container image
   - `docker-compose.yml`: Configuration for local development with Docker

2. **Google Cloud Run Deployment**:
   - `deploy-cloud-run.sh`: Script for deploying to Google Cloud Run
   - `cloudbuild.yaml`: Configuration for Google Cloud Build

3. **PowerShell Deployment Scripts**:
   - Various `.ps1` scripts for different deployment scenarios

## API Endpoints

### Root Endpoint (`/`)
- Returns welcome message and API information

### Document Processing
- `POST /documents/split_images`: Extract images from uploaded documents
- `POST /documents/process`: Process extracted images with AI analysis

### Transcription
- `POST /transcriber/transcribe/`: Transcribe audio files (upload or S3 URL)
- `GET /transcriber/`: Get information about the Transcriber API

## Key Features

### Document Processing
- PDF document parsing and image extraction
- AI-powered image analysis using Google's Gemini Pro Vision model
- S3 integration for document storage

### Audio Transcription
- Support for various audio formats (MP3, WAV, M4A, FLAC, OGG, AAC)
- Multi-language transcription with Google Speech-to-Text
- Audio chunking and parallel processing for efficient transcription
- Noise reduction for improved transcription quality
- Translation of non-English transcriptions to English
- Conversation summarization for transcribed content

### Cloud Integration
- AWS S3 for document and audio storage
- Google Cloud Storage integration
- Google Cloud Run deployment support
- Secret management for secure credential handling

## Technical Implementation Details

### Document Processing
- Uses PyMuPDF (fitz) for PDF parsing and image extraction
- Implements Google's Gemini Pro Vision model for image analysis
- Stores extracted images in AWS S3

### Audio Transcription
- Implements audio format conversion using FFmpeg
- Uses Google Cloud Speech-to-Text for high-quality transcription
- Optimizes processing with audio chunking and parallel execution
- Implements noise reduction for improved audio quality
- Provides language detection and translation services
- Generates conversation summaries using Google's Gemini model

### Deployment
- Docker containerization for consistent deployment
- Google Cloud Run for serverless deployment
- Environment variable configuration for different deployment scenarios
- Secret management for secure credential handling

## Development and Deployment Workflow

1. **Local Development**:
   - Run with Docker: `docker-compose up -d`
   - Run without Docker: `uvicorn main:app --host 0.0.0.0 --port 8080 --reload`

2. **Google Cloud Run Deployment**:
   - Set environment variables
   - Run deployment script: `./deploy-cloud-run.sh`
   - Script handles image building, pushing, and service deployment

3. **Configuration**:
   - Local: `.env` file with required credentials
   - Cloud Run: Environment variables and Secret Manager
