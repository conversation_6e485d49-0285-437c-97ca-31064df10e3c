"""Transcriber Module Entry Point

This is the entry point for the Transcriber module, which initializes the FastAPI app
and registers the API endpoints.
"""

import logging
import threading
import time
from fastapi import FastAPI, APIRouter
from fastapi.middleware.cors import CORSMiddleware

from .api import router as api_router

# Configure logging
logger = logging.getLogger(__name__)

# Create a router for the main application to use
router = APIRouter()

# Include API router in our module router
router.include_router(api_router)

# Create FastAPI app for standalone use
app = FastAPI(title="Transcriber API", description="API for audio transcription")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router in the app with prefix
app.include_router(api_router, prefix="/transcriber")

# Initialize services
from .services import verify_ffmpeg
from .openai_whisper import cleanup_temp_directory

# Verify FFmpeg is installed
verify_ffmpeg()

# Clean up any temporary files from previous runs
logger.info("Cleaning up temporary files from previous runs")
cleanup_temp_directory()

# Set up periodic cleanup task (every 30 minutes)
def periodic_cleanup():
    while True:
        # Sleep for 30 minutes
        time.sleep(1800)
        logger.info("Running periodic cleanup of temporary files")
        cleanup_temp_directory()

# Start the cleanup thread
cleanup_thread = threading.Thread(target=periodic_cleanup, daemon=True)
cleanup_thread.start()
logger.info("Started periodic cleanup thread")

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Welcome to the Transcriber API. Go to /docs for documentation."}
