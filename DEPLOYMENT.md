# Deployment Instructions

This document provides instructions on how to deploy the application to Google Cloud Run.

## Prerequisites

- Docker installed
- Google Cloud SDK installed
- Access to Google Cloud project `call-transcription-summary`

## Build and Push Docker Image

1. Build the Docker image:
   ```bash
   docker build -t gcr.io/call-transcription-summary/leadrat-black-ai-00001-7d4:latest .
   ```

2. Push the Docker image to Google Container Registry:
   ```bash
   docker push gcr.io/call-transcription-summary/leadrat-black-ai-00001-7d4:latest
   ```

## Deploy to Google Cloud Run

Once the Docker image is pushed to Google Container Registry, you can deploy the application to Google Cloud Run using the following command:

### Using PowerShell

```powershell
# Run the PowerShell script
.\deploy-to-cloud-run.ps1
```

### Using Bash (if available)

```bash
# Make the script executable
chmod +x deploy-to-cloud-run.sh

# Run the script
./deploy-to-cloud-run.sh
```

### Manual Deployment

```bash
gcloud run deploy leadrat-black-ai-00001-7d4 \
    --image gcr.io/call-transcription-summary/leadrat-black-ai-00001-7d4:latest \
    --platform managed \
    --region asia-south1 \
    --min-instances 0 \
    --max-instances 10 \
    --memory 2Gi \
    --cpu 1 \
    --timeout 300s \
    --set-env-vars "API_HOST=0.0.0.0" \
    --set-env-vars "API_PORT=8080" \
    --set-env-vars "AWS_REGION=ap-south-1" \
    --set-env-vars "AWS_ACCESS_KEY_ID=********************" \
    --set-env-vars "AWS_SECRET_ACCESS_KEY=XcDOxHtp//5J0eHMc/TL1vKL61P4imzQ4cJs/cpi" \
    --set-env-vars "S3_BUCKET_NAME=leadrat-black" \
    --set-env-vars "S3_BASE_FOLDER=documents/" \
    --set-env-vars "S3_SUBFOLDER=uploads/" \
    --set-env-vars "GCS_BUCKET_NAME=leadrat-black-gcs" \
    --set-env-vars "GCS_FOLDER_NAME=BroucherTempImages/" \
    --set-env-vars "GENAI_API_KEY=AIzaSyAzKhFZknMZvo1CenruisClSaJYFtcJZaY" \
    --set-env-vars "DEBUG=false" \
    --set-env-vars "ENVIRONMENT=production" \
    --set-env-vars "GCP_PROJECT_ID=call-transcription-summary" \
    --allow-unauthenticated
```

## Verify Deployment

After deployment, you can verify that the application is running by accessing the URL provided by Google Cloud Run.
