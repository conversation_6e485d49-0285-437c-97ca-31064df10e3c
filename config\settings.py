import os
import json
from typing import Optional, Dict, Any
from pydantic_settings import BaseSettings
from functools import lru_cache
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Check if we're running in Google Cloud Run
IN_CLOUD_RUN = os.environ.get("K_SERVICE") is not None

# Try to import Google Secret Manager if available
try:
    from google.cloud import secretmanager
    HAS_SECRET_MANAGER = True
except ImportError:
    HAS_SECRET_MANAGER = False
    if IN_CLOUD_RUN:
        logger.warning("Running in Cloud Run but google-cloud-secret-manager is not installed")

def get_secret(project_id: str, secret_id: str, version_id: str = "latest") -> Optional[str]:
    """Get secret from Google Secret Manager."""
    if not HAS_SECRET_MANAGER:
        return None

    try:
        client = secretmanager.SecretManagerServiceClient()
        name = f"projects/{project_id}/secrets/{secret_id}/versions/{version_id}"
        response = client.access_secret_version(request={"name": name})
        return response.payload.data.decode("UTF-8")
    except Exception as e:
        logger.error(f"Error accessing secret {secret_id}: {e}")
        return None

class Settings(BaseSettings):
    # API Settings
    API_HOST: str = "0.0.0.0"  # Always bind to all interfaces
    API_PORT: int = int(os.environ.get("PORT", 8080))  # Use Cloud Run PORT env var

    # AWS Settings
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = os.environ.get("AWS_REGION", "ap-south-1")
    S3_BUCKET_NAME: str = os.environ.get("S3_BUCKET_NAME", "leadrat-black")
    S3_BASE_FOLDER: str = os.environ.get("S3_BASE_FOLDER", "documents/")
    S3_SUBFOLDER: str = os.environ.get("S3_SUBFOLDER", "uploads/")

    # Google Cloud Storage Settings
    GCS_BUCKET_NAME: str = os.environ.get("GCS_BUCKET_NAME", "leadrat-black-gcs")
    GCS_FOLDER_NAME: str = os.environ.get("GCS_FOLDER_NAME", "audio/")

    # Google AI Settings
    GENAI_API_KEY: Optional[str] = None

    # OpenAI Settings
    OPENAI_API_KEY: Optional[str] = None

    # Environment Settings
    DEBUG: bool = os.environ.get("DEBUG", "false").lower() in ("true", "1", "t")
    ENVIRONMENT: str = os.environ.get("ENVIRONMENT", "production")

    # Google Cloud Project ID (for Secret Manager)
    GCP_PROJECT_ID: Optional[str] = os.environ.get("GCP_PROJECT_ID", None)

    class Config:
        env_file = ".env"
        case_sensitive = True

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # If running in Cloud Run and Secret Manager is available, try to get secrets
        if IN_CLOUD_RUN and HAS_SECRET_MANAGER and self.GCP_PROJECT_ID:
            self._load_secrets_from_secret_manager()

        # Validate required settings
        self._validate_settings()

    def _load_secrets_from_secret_manager(self):
        """Load secrets from Google Secret Manager."""
        logger.info("Loading secrets from Google Secret Manager")

        # Map of environment variables to secret names
        secret_map = {
            "AWS_ACCESS_KEY_ID": "aws-access-key-id",
            "AWS_SECRET_ACCESS_KEY": "aws-secret-access-key",
            "GENAI_API_KEY": "genai-api-key",
            "OPENAI_API_KEY": "openai-api-key",
        }

        for env_var, secret_name in secret_map.items():
            if not getattr(self, env_var):
                secret_value = get_secret(self.GCP_PROJECT_ID, secret_name)
                if secret_value:
                    setattr(self, env_var, secret_value)
                    logger.info(f"Loaded {env_var} from Secret Manager")

    def _validate_settings(self):
        """Validate that required settings are available."""
        # For AWS S3, we need credentials unless we're in GCP (which can use workload identity)
        if not IN_CLOUD_RUN and (not self.AWS_ACCESS_KEY_ID or not self.AWS_SECRET_ACCESS_KEY):
            logger.warning("AWS credentials not found. S3 operations may fail.")

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()