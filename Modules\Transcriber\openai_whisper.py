"""
OpenAI Whisper API Integration Module

This module provides integration with OpenAI's Whisper API for audio transcription.
"""

import os
import io
import logging
import tempfile
from typing import Dict, Any, Optional
from config.settings import settings

# Configure logging
logger = logging.getLogger(__name__)

# Import OpenAI client
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI package not available. Please install it with: pip install openai")

# Since we're using OpenAI Whisper translations endpoint, we don't need additional translation
TRANSLATION_AVAILABLE = False
logger.info("Using OpenAI Whisper translations endpoint - no additional translation needed.")

# Create temp directory if it doesn't exist
TEMP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
os.makedirs(TEMP_DIR, exist_ok=True)

def transcribe_with_openai_whisper(file_path: str) -> Dict[str, Any]:
    """
    Transcribe audio using OpenAI's Whisper API.

    Args:
        file_path: Path to the audio file

    Returns:
        Dictionary with transcription results
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"Audio file not found: {file_path}")
            return {
                "text": f"Error: Audio file not found: {file_path}",
                "language": "en",
                "translated_text": f"Error: Audio file not found",
                "duration": 0.0,
                "openai_whisper": False
            }

        # Check if file is empty
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.error(f"Audio file is empty (0 bytes): {file_path}")
            return {
                "text": f"Error: Audio file is empty (0 bytes)",
                "language": "en",
                "translated_text": f"Error: Audio file is empty",
                "duration": 0.0,
                "openai_whisper": False
            }

        logger.info(f"Processing audio file: {file_path} (size: {file_size} bytes)")

        # Check if OpenAI package is available
        if not OPENAI_AVAILABLE:
            logger.error("OpenAI package not available")
            return {
                "text": f"Error: OpenAI package not installed",
                "language": "en",
                "translated_text": f"Error: OpenAI package not installed",
                "duration": 0.0,
                "openai_whisper": False
            }

        # Check if OpenAI API key is available
        if not settings.OPENAI_API_KEY:
            logger.error("OpenAI API key not found in settings")
            return {
                "text": f"Error: OpenAI API key not configured",
                "language": "en",
                "translated_text": f"Error: OpenAI API key not configured",
                "duration": 0.0,
                "openai_whisper": False
            }

        # Check file size limit (OpenAI Whisper API has a 25MB limit)
        max_file_size = 25 * 1024 * 1024  # 25MB in bytes
        if file_size > max_file_size:
            logger.error(f"Audio file is too large: {file_size} bytes (max: {max_file_size} bytes)")
            return {
                "text": f"Error: Audio file is too large (max 25MB)",
                "language": "en",
                "translated_text": f"Error: Audio file is too large",
                "duration": 0.0,
                "openai_whisper": False
            }

        # Initialize OpenAI client
        try:
            client = OpenAI(api_key=settings.OPENAI_API_KEY)
            logger.info("OpenAI client initialized successfully")
        except Exception as client_error:
            logger.error(f"Error initializing OpenAI client: {str(client_error)}")
            return {
                "text": f"Error: Could not initialize OpenAI client: {str(client_error)}",
                "language": "en",
                "translated_text": f"Error: Could not initialize OpenAI client",
                "duration": 0.0,
                "openai_whisper": False
            }

        # Use translations endpoint to get English output regardless of input language
        try:
            logger.info(f"Sending request to OpenAI Whisper Translations API for file: {file_path}")
            with open(file_path, 'rb') as audio_file:
                transcript = client.audio.translations.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="verbose_json"
                )
            logger.info(f"Received response from OpenAI Whisper Translations API")
        except Exception as api_error:
            logger.error(f"Error calling OpenAI Whisper Translations API: {str(api_error)}")
            return {
                "text": f"Error: OpenAI API request failed: {str(api_error)}",
                "language": "en",
                "translated_text": f"Error: OpenAI API request failed",
                "duration": 0.0,
                "openai_whisper": False
            }

        # Extract transcription and metadata from the response
        try:
            result = transcript.model_dump() if hasattr(transcript, 'model_dump') else transcript.__dict__
            logger.info(f"Successfully parsed OpenAI API response")
        except Exception as parse_error:
            logger.error(f"Error parsing OpenAI API response: {str(parse_error)}")
            return {
                "text": f"Error: Could not parse OpenAI API response",
                "language": "en",
                "translated_text": f"Error: Could not parse API response",
                "duration": 0.0,
                "openai_whisper": False
            }

        # Extract transcription and metadata
        # Note: Since we're using the translations endpoint, the text is already in English
        transcription = result.get("text", "").strip()
        detected_language = result.get("language", "en")  # Original language detected
        duration = result.get("duration", 0.0)

        logger.info(f"Original language detected: {detected_language}")
        logger.info(f"Audio duration: {duration} seconds")
        logger.info(f"English transcription: {transcription[:100]}...")  # Log first 100 chars

        # If transcription is empty, return an error
        if not transcription:
            logger.error("Transcription is empty")
            return {
                "text": "Error: No speech detected in the audio file",
                "language": detected_language,
                "translated_text": "Error: No speech detected in the audio file",
                "duration": duration,
                "openai_whisper": False
            }

        # Since we used the translations endpoint, the text is already in English
        # No additional translation needed
        translated_text = transcription
        logger.info(f"Using OpenAI Whisper translations endpoint - text is already in English")

        # Extract segments if available
        segments = []
        if "segments" in result:
            for segment in result["segments"]:
                segments.append({
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "text": segment.get("text", "")
                })

        # Return the transcription results
        return {
            "text": transcription,  # Already in English due to translations endpoint
            "original_text": transcription,  # Since we used translations, this is the English version
            "original_language": detected_language,  # The detected original language
            "language": "en",  # Always English since we used translations endpoint
            "translated_text": translated_text,  # Same as text since already translated
            "duration": duration,
            "segments": segments,
            "openai_whisper": True,  # Flag to indicate this was processed with OpenAI Whisper
            "translations_endpoint": True  # Flag to indicate we used the translations endpoint
        }

    except Exception as e:
        logger.error(f"Error in OpenAI Whisper transcription: {str(e)}")
        return {
            "text": f"Error transcribing with OpenAI Whisper: {str(e)}",
            "original_text": f"Error transcribing with OpenAI Whisper: {str(e)}",
            "language": "en",
            "translated_text": f"Error transcribing with OpenAI Whisper: {str(e)}",
            "duration": 0.0,
            "openai_whisper": False
        }

def cleanup_temp_directory():
    """
    Clean up all temporary files in the TEMP_DIR.
    This should be called after each API response is sent to ensure no files are left behind.
    """
    try:
        logger.info(f"Cleaning up temporary directory: {TEMP_DIR}")
        count = 0
        for filename in os.listdir(TEMP_DIR):
            file_path = os.path.join(TEMP_DIR, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)
                    count += 1
            except Exception as e:
                logger.warning(f"Failed to delete temporary file {file_path}: {str(e)}")
        logger.info(f"Cleaned up {count} temporary files")
    except Exception as e:
        logger.error(f"Error cleaning up temporary directory: {str(e)}")

# File handling functions
def download_from_url(url: str) -> Optional[str]:
    """
    Download a file from a URL and save it to a temporary file.

    Args:
        url: The URL to download from

    Returns:
        The path to the downloaded file if successful, None otherwise
    """
    try:
        import requests
        import tempfile
        import urllib.parse

        logger.info(f"Downloading file from URL: {url}")

        # Check if this is an S3 URL and handle it appropriately
        if _is_s3_url(url):
            logger.info("Detected S3 URL, attempting S3 download")
            return _download_from_s3_url(url)

        # For regular HTTP/HTTPS URLs
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        # Try to determine file extension from URL or Content-Type
        file_ext = _get_file_extension_from_url_or_headers(url, response.headers)

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext, dir=TEMP_DIR) as temp_file:
            local_path = temp_file.name

            # Download the file
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)

        logger.info(f"Successfully downloaded file from {url} to {local_path}")
        return local_path

    except Exception as e:
        logger.error(f"Error downloading file from {url}: {str(e)}")
        return None

def save_uploaded_file(file_content: bytes, filename: str = None) -> Optional[str]:
    """
    Save uploaded file content to a temporary file.

    Args:
        file_content: The file content as bytes
        filename: Original filename to preserve extension (optional)

    Returns:
        The path to the saved file if successful, None otherwise
    """
    try:
        import tempfile
        import os

        # Determine file extension from filename
        file_ext = '.tmp'  # Default fallback
        if filename:
            _, ext = os.path.splitext(filename)
            if ext:
                file_ext = ext.lower()
                logger.info(f"Using file extension from filename: {file_ext}")
            else:
                logger.warning(f"No extension found in filename: {filename}")

        # Create a temporary file with the correct extension
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext, dir=TEMP_DIR) as temp_file:
            local_path = temp_file.name
            temp_file.write(file_content)

        logger.info(f"Successfully saved uploaded file to {local_path} with extension {file_ext}")
        return local_path

    except Exception as e:
        logger.error(f"Error saving uploaded file: {str(e)}")
        return None

def _is_s3_url(url: str) -> bool:
    """
    Check if a URL is an S3 URL.

    Args:
        url: The URL to check

    Returns:
        True if it's an S3 URL, False otherwise
    """
    return (url.startswith('s3://') or
            ('.s3.' in url and '.amazonaws.com' in url) or
            ('s3.amazonaws.com' in url))

def _download_from_s3_url(s3_url: str) -> Optional[str]:
    """
    Download a file from an S3 URL using the S3Service.

    Args:
        s3_url: The S3 URL to download from

    Returns:
        The path to the downloaded file if successful, None otherwise
    """
    try:
        from .services import S3Service

        s3_service = S3Service()
        local_path = s3_service.download_from_s3(s3_url)

        if local_path and os.path.exists(local_path):
            logger.info(f"Successfully downloaded S3 file to {local_path}")
            return local_path
        else:
            logger.error(f"Failed to download from S3 URL: {s3_url}")
            return None

    except Exception as e:
        logger.error(f"Error downloading from S3 URL {s3_url}: {str(e)}")
        return None

def _get_file_extension_from_url_or_headers(url: str, headers: dict) -> str:
    """
    Try to determine file extension from URL or HTTP headers.

    Args:
        url: The URL
        headers: HTTP response headers

    Returns:
        File extension (with dot) or '.tmp' if unknown
    """
    import urllib.parse
    import os

    # First try to get extension from URL
    parsed_url = urllib.parse.urlparse(url)
    path = parsed_url.path
    if path:
        _, ext = os.path.splitext(path)
        if ext:
            return ext.lower()

    # Try to get from Content-Type header
    content_type = headers.get('content-type', '').lower()
    if 'audio/mpeg' in content_type or 'audio/mp3' in content_type:
        return '.mp3'
    elif 'audio/wav' in content_type:
        return '.wav'
    elif 'audio/mp4' in content_type or 'audio/m4a' in content_type:
        return '.m4a'
    elif 'audio/ogg' in content_type:
        return '.ogg'
    elif 'audio/flac' in content_type:
        return '.flac'

    # Default to .tmp if we can't determine
    return '.tmp'
